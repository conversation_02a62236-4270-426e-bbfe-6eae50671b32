define(["exports","./when-54c2dc71","./Check-6c0211bc","./Math-fc8cecf5","./Cartesian2-bddc1162"],function(t,p,a,U,S){"use strict";function b(t,a,i,n,e,s,r){var h,i=(h=t)*(i=i)*(4+h*(4-3*i))/16;return(1-i)*t*a*(n+i*e*(r+i*s*(2*r*r-1)))}var q=new S.Cartesian3,w=new S.Cartesian3;function n(t,a,i,n){var e,s,r,h,o,d,c,u,M,l,g,_,p,f,m,v,C,H,O;S.Cartesian3.normalize(n.cartographicToCartesian(a,w),q),S.Cartesian3.normalize(n.cartographicToCartesian(i,w),w);!function(t,a,i,n,e,s,r){var h=(a-i)/a,o=s-n,n=Math.atan((1-h)*Math.tan(e)),e=Math.atan((1-h)*Math.tan(r)),r=Math.cos(n),n=Math.sin(n),d=Math.cos(e),e=Math.sin(e),c=r*d,u=r*e,M=n*e,l=n*d,g=o,_=U.CesiumMath.TWO_PI,p=Math.cos(g),f=Math.sin(g);do{p=Math.cos(g),f=Math.sin(g);var m,v,C=u-l*p,H=Math.sqrt(d*d*f*f+C*C),O=M+c*p,S=Math.atan2(H,O),_=g,q=O-2*M/(v=0===H?(m=0,1):1-(m=c*f/H)*m)}while(isNaN(q)&&(q=0),g=o+b(h,m,v,S,H,O,q),Math.abs(g-_)>U.CesiumMath.EPSILON12);n=i*(1+(e=v*(a*a-i*i)/(i*i))*(4096+e*(e*(320-175*e)-768))/16384)*(S-(n=e*(256+e*(e*(74-47*e)-128))/1024)*H*(q+n*(O*(2*(a=q*q)-1)-n*q*(4*H*H-3)*(4*a-3)/6)/4)),a=Math.atan2(d*f,u-l*p),r=Math.atan2(r*f,u*p-l),t._distance=n,t._startHeading=a,t._endHeading=r,t._uSquared=e}(t,n.maximumRadius,n.minimumRadius,a.longitude,a.latitude,i.longitude,i.latitude),t._start=S.Cartographic.clone(a,t._start),t._end=S.Cartographic.clone(i,t._end),t._start.height=0,t._end.height=0,s=(e=t)._uSquared,r=e._ellipsoid.maximumRadius,h=e._ellipsoid.minimumRadius,o=(r-h)/r,d=Math.cos(e._startHeading),c=Math.sin(e._startHeading),u=(1-o)*Math.tan(e._start.latitude),M=1/Math.sqrt(1+u*u),l=M*u,g=Math.atan2(u,d),f=1-(p=(_=M*c)*_),m=Math.sqrt(f),H=1-3*(v=s/4)+35*(C=v*v)/4,O=1-5*v,s=(i=1+v-3*C/4+5*(n=C*v)/4-175*(a=C*C)/64)*g-(t=1-v+15*C/8-35*n/8)*Math.sin(2*g)*v/2-H*Math.sin(4*g)*C/16-O*Math.sin(6*g)*n/48-5*Math.sin(8*g)*a/512,(e=e._constants).a=r,e.b=h,e.f=o,e.cosineHeading=d,e.sineHeading=c,e.tanU=u,e.cosineU=M,e.sineU=l,e.sigma=g,e.sineAlpha=_,e.sineSquaredAlpha=p,e.cosineSquaredAlpha=f,e.cosineAlpha=m,e.u2Over4=v,e.u4Over16=C,e.u6Over64=n,e.u8Over256=a,e.a0=i,e.a1=t,e.a2=H,e.a3=O,e.distanceRatio=s}function i(t,a,i){i=p.defaultValue(i,S.Ellipsoid.WGS84);this._ellipsoid=i,this._start=new S.Cartographic,this._end=new S.Cartographic,this._constants={},this._startHeading=void 0,this._endHeading=void 0,this._distance=void 0,this._uSquared=void 0,p.defined(t)&&p.defined(a)&&n(this,t,a,i)}Object.defineProperties(i.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},surfaceDistance:{get:function(){return this._distance}},start:{get:function(){return this._start}},end:{get:function(){return this._end}},startHeading:{get:function(){return this._startHeading}},endHeading:{get:function(){return this._endHeading}}}),i.prototype.setEndPoints=function(t,a){n(this,t,a,this._ellipsoid)},i.prototype.interpolateUsingFraction=function(t,a){return this.interpolateUsingSurfaceDistance(this._distance*t,a)},i.prototype.interpolateUsingSurfaceDistance=function(t,a){var i=this._constants,n=i.distanceRatio+t/i.b,e=Math.cos(2*n),s=Math.cos(4*n),r=Math.cos(6*n),h=Math.sin(2*n),o=Math.sin(4*n),d=Math.sin(6*n),c=Math.sin(8*n),u=n*n,M=n*u,l=i.u8Over256,g=i.u2Over4,_=i.u6Over64,t=i.u4Over16,u=2*M*l*e/3+n*(1-g+7*t/4-15*_/4+579*l/64-(t-15*_/4+187*l/16)*e-(5*_/4-115*l/16)*s-29*l*r/16)+(g/2-t+71*_/32-85*l/16)*h+(5*t/16-5*_/4+383*l/96)*o-u*((_-11*l/2)*h+5*l*o/2)+(29*_/96-29*l/16)*d+539*l*c/1536,h=Math.asin(Math.sin(u)*i.cosineAlpha),o=Math.atan(i.a/i.b*Math.tan(h));u-=i.sigma;_=Math.cos(2*i.sigma+u),d=Math.sin(u),l=Math.cos(u),c=i.cosineU*l,h=i.sineU*d,_=Math.atan2(d*i.sineHeading,c-h*i.cosineHeading)-b(i.f,i.sineAlpha,i.cosineSquaredAlpha,u,d,l,_);return p.defined(a)?(a.longitude=this._start.longitude+_,a.latitude=o,a.height=0,a):new S.Cartographic(this._start.longitude+_,o,0)},t.EllipsoidGeodesic=i});
