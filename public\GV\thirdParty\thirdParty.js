(function () {
    let thirdJsArr = [
        'CesiumManager/Cesium.js',
        'Threejs-109/build/three.js',
        'Threejs-109/examples/js/loaders/SVGLoader.js',

        // 智慧城市使用库
        'Threejs-109/custom/dat.gui.module.js',
        'Threejs-109/examples/js/libs/stats.min.js',
        'Threejs-109/examples/js/postprocessing/EffectComposer.js',
        'Threejs-109/examples/js/postprocessing/RenderPass.js',
        'Threejs-109/examples/js/postprocessing/UnrealBloomPass.js',
        'Threejs-109/examples/js/postprocessing/ShaderPass.js',
        'Threejs-109/examples/js/postprocessing/ClearPass.js',
        'Threejs-109/examples/js/postprocessing/SMAAPass.js',
        'Threejs-109/examples/js/shaders/LuminosityHighPassShader.js',
        'Threejs-109/examples/js/shaders/CopyShader.js',
        'Threejs-109/examples/js/shaders/ParallaxShader.js',
        'Threejs-109/examples/js/shaders/FXAAShader.js',
        'Threejs-109/examples/js/shaders/SMAAShader.js',
        'Threejs-109/examples/js/postprocessing/SSAARenderPass.js',
        'Threejs-109/custom/shp.js',
        // 'Threejs-109/custom/ThreeManager.js',
        // 'Threejs-109/custom/ParseDataToGeometry.js',
        'Threejs-109/custom/tween.umd.js',
        // 'Threejs-109/custom/spector.bundle.js',
        'Threejs-109/custom/ParticleEngine.js',
        'Threejs-109/custom/ParticleEngineExamples.js',
        'Threejs-109/examples/js/objects/Water.js',
        'Threejs-109/examples/js/objects/Water2.js',
        // 'Threejs-109/examples/js/objects/Water.js',
        'Threejs-109/examples/js/objects/Reflector.js',
        'Threejs-109/examples/js/objects/Refractor.js',
        'Threejs-109/examples/js/renderers/CSS3DRenderer.js',
        'Threejs-109/examples/js/lines/LineSegments2.js',
        'Threejs-109/examples/js/lines/LineSegmentsGeometry.js',
        'Threejs-109/examples/js/lines/LineGeometry.js',
        'Threejs-109/examples/js/lines/LineMaterial.js',
        'Threejs-109/examples/js/lines/Line2.js',
        //矢量瓦片使用库
        './ol.js'
    ]
    let thirdCssArr = [
        'CesiumManager/Widgets/widgets.css'
    ]
    let scriptArr = Array.from(document.getElementsByTagName('script'))
    let host
    scriptArr.map(item => {
        let src = item.getAttribute('src')
        if (item.src.match('thirdParty.js')) {
            host = src.split('thirdParty.js')[0]
        }
    })
    thirdLoad(thirdCssArr, "css")
    thirdLoad(thirdJsArr, "js")
    function thirdLoad(arr, type) {
        let head = document.getElementsByTagName('head')[0]
        let fragment = document.createDocumentFragment()
        if (type === "js") {
            for (let i = 0; i < arr.length; i++) {
                document.write(`<script src='${host}${arr[i]}'></script>`)
            }
        } else if (type === "css") {
            for (let i = 0; i < arr.length; i++) {
                let link = document.createElement('link')
                link.href = `${host}${arr[i]}`
                link.rel = "stylesheet"
                link.type = "text/css"
                fragment.appendChild(link)
            }
            head.appendChild(fragment)
        }
    }

})()

