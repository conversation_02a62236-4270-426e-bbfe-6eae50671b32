import request from '@/config/axios'

// 查询训练计划列表
export const getPlanList = (params: Object | undefined) => {
  return request.get({ url: '/gacs/training-plan/page', params })
}

// 新增训练计划
export const createPlan = (data: Object | undefined) => {
  return request.post({ url: '/gacs/training-plan/create', data })
}

// 修改训练计划
export const updatePlan = (data: Object | undefined) => {
  return request.put({ url: `/gacs/training-plan/update`, data })
}

// 删除训练计划
export const deletePlan = (id: number) => {
  return request.delete({ url: '/gacs/training-plan/delete?id=' + id })
}

// 查看训练计划
export const getPlan = (params: Object | undefined) => {
  return request.download({ url: '/gacs/training-plan/get', params })
}
