import request from '@/config/axios'

// 查询训练任务列表
export const getTaskList = (params: Object | undefined) => {
  return request.get({ url: '/gacs/training-mission/page', params })
}
// 查询训练任务
export const getTask = (params: Object | undefined) => {
  return request.get({ url: '/gacs/training-mission/create', params})
}
// 新增训练任务
export const createTask = (data: Object | undefined) => {
  return request.post({ url: '/gacs/training-mission/create', data })
}
// 修改训练任务
export const updateTask = (data: Object | undefined) => {
  return request.put({ url: `/gacs/training-mission/update`, data })
}
// 删除训练任务
export const deleteTask = (id: number) => {
  return request.delete({ url: '/gacs/training-mission/delete?id=' + id })
}
