import request from '@/config/axios'

// 上传文件
export const uploadFileRawApi = (data: object | undefined) => {
  return request.post({url: `/api/v1/virtual-file`, data});
};
// 获取文件内容
export const getFileRawApi = (id: string) => {
  return request.get({url: `/api/v1/virtual-file/${id}/json`});
};
// 获取文件名称
export const getFileNameApi = (params: object | undefined) => {
  return request.get({url: `/api/v1/metadata/file`, params});
};