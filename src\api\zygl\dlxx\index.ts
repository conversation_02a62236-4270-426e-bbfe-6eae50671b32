import request from '@/config/axios'

// 获取服务列表
export const getListApi = (params: Object | undefined) => {
  return request.get({ url: '/api/v1/layers', params, type: 'IC' })
}
// 获取磁盘路径列表
export const getDiskPathsApi = (data: Object | undefined) => {
  return request.post({ url: '/api/v1/files/getSystemDirs', data, type: 'IC' })
}
// 根据磁盘路径获取数据信息
export const getDiskPathDataApi = (params: Object | undefined) => {
  return request.get({ url: '/api/v1/layers/mbtiles/metadata', params, type: 'IC' })
}
// 发布服务
export const releaseApi = (data: Object | undefined) => {
  return request.post({ url: '/api/v1/layers/mbtiles', data, type: 'IC' })
}
// 发布服务
export const getApi = (id: number) => {
  return request.get({ url: `/api/v1/layers/${id}`, type: 'IC' })
}
// 删除服务
export const deleteApi = (id: number) => {
  return request.delete({ url: `/api/v1/layers/${id}`, type: 'IC' })
}
