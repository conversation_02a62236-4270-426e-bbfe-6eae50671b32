import request from '@/config/axios'
const { ONESIMResourceUrl } = globalConfig
// 获取模型树
export const getPlatformTreeApi = (params: Object | undefined) => {
  return request.get({ url: '/api/v1/platform-type/tree', params, type: 'ONESIM' })
}
// 获取组件树
export const getCompTreeApi = (params: Object | undefined) => {
  return request.get({ url: '/api/v1/component/tree', params, type: 'ONESIM' })
}
// 获取模型详情
export const getPlatformDataApi = (id: string) => {
  return request.get({ url: `/api/v1/platform-type/${id}`, undefined, type: 'ONESIM' })
}
// 获取组件详情
export const getCompDataApi = (id: string) => {
  return request.get({ url: `/api/v1/component/${id}`, undefined, type: 'ONESIM' })
}
// 获取模型资源详情
export const getModelDataApi = (id: string) => {
  return request.get({ url: `/api/v1/model-data/${id}`, undefined, type: 'ONESIM' })
}
// 获取图标缩略图
export const getIconThumbApi = (id: string) => {
  return `${ONESIMResourceUrl}/api/v1/model3d/${id}/thumb`
}
