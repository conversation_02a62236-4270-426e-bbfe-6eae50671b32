import request from '@/config/axios'

// 查询席位列表
export const getListApi = (params: Object | undefined) => {
  return request.get({ url: '/gacs/seat/page', params })
}
// 查询席位列表
export const getListStatApi = () => {
  return request.get({ url: '/gacs/seat/stat' })
}
// 查询席位
export const getApi = (params: Object | undefined) => {
  return request.get({ url: '/gacs/seat/create', params })
}
// 新增席位
export const createApi = (data: Object | undefined) => {
  return request.post({ url: '/gacs/seat/create', data })
}
// 修改席位
export const updateApi = (data: Object | undefined) => {
  return request.put({ url: `/gacs/seat/update`, data })
}
// 删除席位
export const deleteApi = (id: number) => {
  return request.delete({ url: '/gacs/seat/delete?id=' + id })
}
// 启动席位
export const upApi = (data: Object | undefined) => {
  return request.post({ url: '/gacs/seat/up', data })
}
// 关闭席位
export const downApi = (data: Object | undefined) => {
  return request.post({ url: '/gacs/seat/down', data })
}
// 全部启动席位
export const allUpApi = () => {
  return request.post({ url: '/gacs/seat/upAll' })
}
// 全部关闭席位
export const allDownApi = () => {
  return request.post({ url: '/gacs/seat/downAll' })
}
