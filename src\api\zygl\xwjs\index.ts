import request from '@/config/axios'

// 查询席位角色列表
export const getListApi = (params: Object | undefined) => {
  return request.get({ url: '/gacs/seat-role/page', params })
}
// 查询席位角色
export const getApi = (params: Object | undefined) => {
  return request.get({ url: '/gacs/seat-role/get', params})
}
// 新增席位角色
export const createApi = (data: Object | undefined) => {
  return request.post({ url: '/gacs/seat-role/create', data })
}
// 修改席位角色
export const updateApi = (data: Object | undefined) => {
  return request.put({ url: `/gacs/seat-role/update`, data })
}
// 删除席位角色
export const deleteApi = (id: number) => {
  return request.delete({ url: '/gacs/seat-role/delete?id=' + id })
}
