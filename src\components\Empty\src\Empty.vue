<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'

defineOptions({ name: 'Empty' })

const props = defineProps({
  width: propTypes.string.def('100'),
  fontSize: propTypes.string.def('14px')
})
</script>
<template>
  <img class="mb-5px" src="/images/empty_img.png" alt="" :width="props.width" />
  <p class="empty-text" :class="[`text-${props.fontSize}`]">暂无更多信息</p>
</template>
<style lang="less" scoped>
.empty-text {
  color: #909399;
}
</style>
