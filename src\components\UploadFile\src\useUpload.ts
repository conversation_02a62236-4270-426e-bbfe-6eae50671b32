import * as FileApi from '@/api/infra/file'
import { UploadRequestOptions } from 'element-plus/es/components/upload/src/upload'

/**
 * 获得上传 URL
 */
export const getUploadUrl = (): string => {
  return globalConfig.BaseResourceUrl + import.meta.env.VITE_API_URL + '/infra/file/upload'
}

export const useUpload = () => {
  // 后端上传地址
  const uploadUrl = getUploadUrl()
  // 重写ElUpload上传方法
  const httpRequest = async (options: UploadRequestOptions) => {
    // 模式二：后端上传
    // 重写 el-upload httpRequest 文件上传成功会走成功的钩子，失败走失败的钩子
    return new Promise((resolve, reject) => {
      FileApi.updateFile({ file: options.file })
        .then((res) => {
          if (res.code === 0) {
            resolve(res)
          } else {
            reject(res)
          }
        })
        .catch((res) => {
          reject(res)
        })
    })
  }

  return {
    uploadUrl,
    httpRequest
  }
}