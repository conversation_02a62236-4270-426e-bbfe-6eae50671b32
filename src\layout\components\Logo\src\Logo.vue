<script lang="ts" setup>
import { computed, onMounted, ref, unref, watch } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'

defineOptions({ name: 'Logo' })

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('logo')

const appStore = useAppStore()

const show = ref(true)

const title = computed(() => appStore.getTitle)

const layout = computed(() => appStore.getLayout)

const collapse = computed(() => appStore.getCollapse)

onMounted(() => {
  if (unref(collapse)) show.value = false
})

watch(
  () => collapse.value,
  (collapse: boolean) => {
    if (unref(layout) === 'topLeft' || unref(layout) === 'cutMenu') {
      show.value = true
      return
    }
    if (!collapse) {
      setTimeout(() => {
        show.value = !collapse
      }, 400)
    } else {
      show.value = !collapse
    }
  }
)

watch(
  () => layout.value,
  (layout) => {
    if (layout === 'top' || layout === 'cutMenu') {
      show.value = true
    } else {
      if (unref(collapse)) {
        show.value = false
      } else {
        show.value = true
      }
    }
  }
)
</script>

<template>
  <div :class="[
    prefixCls,
    layout !== 'classic' ? `${prefixCls}__Top` : '',
    'c-logo flex !h-[var(--logo-height)] items-center pl-5px relative decoration-none overflow-hidden pr-70px max-w-460px min-w-264px'
  ]">
    <img src="/images/default_logo.png" width="34" />
    <div v-if="show" :class="[
    'ml-13px text-30px font-700',
    {
      'text-[var(--logo-title-text-color)]': layout === 'classic',
      'text-[var(--top-header-text-color)]':
        layout === 'topLeft' || layout === 'top' || layout === 'cutMenu'
    }

  ]" :style="{ letterSpacing: '2px' }">
      {{ title }}
    </div>
  </div>
</template>
<style lang="scss" scoped>
.c-logo {
  background: url('/images/sys_title_bg.png') no-repeat 0 0 / 100% 100%;

}
</style>
