@use './var.css';
@use './FormCreate/index.scss';
@use './theme.scss';
@use 'element-plus/theme-chalk/dark/css-vars.css';

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}
// 滚动条
::-webkit-scrollbar-thumb {
  width: 10px;
  height: 30px;
  background-color: #097cbc;
  border-radius: 5px;
}

.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow:
      0 0 10px var(--el-color-primary),
      0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}

.el-dropdown-menu {
  --el-bg-color-overlay: #003f63;
  --el-text-color-regular: #fff;
  --el-dropdown-menuItem-hover-fill: transparent;
  --el-border-color-lighter: var(--app-border-color);
}

.el-popper {
  --el-border-color-light: var(--app-border-color);
}

.el-card {
  --el-card-bg-color: transparent;
  --el-card-border-radius: 0;
  --el-card-border-color: var(--app-border-color);
  --el-text-color-primary: #fff;
  border: unset;
  border-top: 1px solid var(--app-border-color);
}

.el-table {
  --el-table-header-bg-color: #00598d;
  --el-table-bg-color: transparent;
  --el-table-border-color: #25344b;
  --el-table-text-color: #fff;
  --el-table-header-text-color: #fff;
  --el-table-row-hover-bg-color: #3c75c3;
  --el-table-current-row-bg-color: #0075b8a6;
  --el-table-tr-bg-color: transparent;
  --el-table-tr-bg-striped-color: #003f64;
  --el-fill-color-lighter: #003f64;
  --el-table-border: 1px solid transparent;
  // --el-table-border-color: var(--app-border-color);
  thead th {
    font-weight: unset;
  }
  .el-table__header {
    .el-table__cell {
      height: 36px;
      padding: 8px 0;
      .cell {
        font-size: 16px;
        color: #fff;
        font-family: Regular;
      }
    }
  }
  .el-table__body {
    width: unset !important;
    .el-table__row {
      font-size: 15px;
      .el-table__cell {
        height: 36px;
        padding: 5px 0;
        .cell {
          .el-table__placeholder {
            width: unset;
            padding-left: 16px;
          }
        }
      }
    }
  }
}
.el-table-v2 {
  --el-table-row-hover-bg-color: #3c75c3 !important;
  --el-bg-color: transparent;
  --el-table-border-color: #25344b !important;
  // --el-table-border: 1px solid transparent !important;
  --el-table-header-bg-color: #00598d !important;
  --el-table-header-text-color: #fff !important;
  .el-table-v2__header-wrapper {
    background: #00598d;
  }
}
.el-form,
.el-form-item--default {
  --el-text-color-regular: #fff;
  --font-size: 16px;
}

.el-button {
  --el-button-bg-color: #125d8d;
  --el-button-hover-bg-color: #125d8d;
  --el-button-hover-text-color: #fff;
  --el-border-radius-base: 0;
  --el-border-color: #36b6ff;
  --el-button-text-color: #fff;
  --el-button-disabled-bg-color: #125d8d;
  --el-button-disabled-border-color: var(--app-border-color)
  height: 30px;
  font-family: Regular;
}

.el-input {
  --el-component-size-large: 40px;
  --el-component-size-small: 26px;
  --el-border-color: var(--app-border-color);
  --el-input-border-radius: 0;
  --el-disabled-border-color: var(--el-border-color);
  --el-input-text-color: #fff;
  --el-input-bg-color: transparent;
  --el-fill-color-light: transparent;
  background-image: linear-gradient(0deg, #0c5e8e 0%, #00456d 75%);
  .el-input__inner {
    font-size: 14px;
  }
}
.el-input.is-disabled .el-input__wrapper,
.el-textarea.is-disabled .el-textarea__inner {
  background-image: linear-gradient(0deg, #4e6f82 -20%, #05344e 100%);
}
.el-textarea {
  --el-input-bg-color: transparent;
  --el-input-border-color: #4a86d2;
  --el-input-focus-border-color: #409eff;
  --el-disabled-border-color: var(--el-input-border-color);
}

.el-input-number {
  --el-fill-color-light: transparent;
  --el-border-radius-base: 0;
  --el-border: 1px solid var(--app-border-color);
}

.el-pagination {
  --el-pagination-button-width: 30px;
  --el-pagination-button-height: 30px;
  --el-pagination-button-bg-color: #043755;
  --el-pagination-button-disabled-bg-color: #043755;
  --el-pagination-text-color: #fff;
  --el-text-color-regular: #fff;
  --el-pagination-button-color: #fff;
  --el-disabled-bg-color: #043755;
  --el-color-primary: #145d89;
  --el-fill-color-blank: #043755;
  --el-border-color: #36b6ff;
  --el-border-radius-base: 0;
  --el-pagination-border-radius: 0;
  .btn-prev,
  .btn-next {
    border: solid 1px var(--el-border-color);
  }

  .el-pager {
    li {
      border: solid 1px var(--el-border-color);
    }
  }
  .el-pagination__sizes {
    .el-select {
      .el-select__wrapper {
        min-height: var(--el-pagination-button-height);
      }
    }
  }
}

.el-dialog {
  --el-dialog-bg-color: #022e48;
  --el-dialog-padding-primary: 0;
  --el-dialog-header-bg: #12577f;
  --el-dialog-border-color: #36b6ff;
  --el-text-color-regular: #fff;
  color: #fff;
  border: 1px solid var(--el-dialog-border-color);
  .el-dialog__header {
    background: var(--el-dialog-header-bg);
  }
  .el-dialog__body {
    padding: 15px 10px 0;
  }
  .el-dialog__footer {
    margin-bottom: 15px;
    text-align: center;
  }
}

.el-loading-mask {
  --el-mask-color: #032a45;
}

.el-select {
  --el-border-color: var(--app-border-color);
  --el-border-radius-base: 0;
  .el-select__wrapper {
    background-image: linear-gradient(0deg, #0c5e8e 0%, #00456d 75%);
    .el-select__tags-text {
      font-size: 14px;
    }
  }
}

.el-select__popper,
.el-cascader__dropdown,
.el-picker__popper {
  --el-bg-color-overlay: #00598c;
  --el-text-color-regular: #fff;
  --el-fill-color-light: #0a79b8;
  --el-text-color-primary: var(--el-color-primary);
  --el-datepicker-border-color: var(--app-border-color);
  .el-select-dropdown__item.is-selected,
  .el-cascader-node.in-active-path,
  .el-cascader-node.is-active {
    font-weight: unset;
  }
  .el-date-table-cell__text {
    font-size: 14px;
  }
}

.el-date-editor {
  --el-input-border-color: #4a86d2;
  --el-border-radius-base: 0;
  --el-bg-color-overlay: #00598c;

  background-image: linear-gradient(0deg, #0c5e8e 0%, #00456d 75%);
}
.el-date-range-picker {
  --el-datepicker-inrange-bg-color: #0a79b8;
  --el-datepicker-inrange-hover-bg-color: var(--el-datepicker-inrange-bg-color);
  --el-fill-color-blank: #0a79b8;
  .el-picker-panel__footer {
    --el-fill-color: #0a79b8;
  }
  .el-button.is-plain {
    --el-button-hover-text-color: #fff;
  }
}

.el-message-box {
  --el-bg-color: #022e48;
  --el-messagebox-title-color: #fff;
  --el-messagebox-content-color: #fff;
  border: 1px solid var(--app-border-color);
}

.el-tree {
  --el-fill-color-blank: transparent;
  --el-tree-node-hover-bg-color: #00a2ff66;
  --el-tree-text-color: #fff;
  --el-color-primary-light-9: var(--el-tree-node-hover-bg-color);
}

.el-upload-dragger {
  --el-fill-color-blank: transparent;
  --el-border-color: var(--app-border-color);
  --el-disabled-bg-color: transparent;
  --el-fill-color-light: transparent;
}

.el-popper.is-dark {
  --el-text-color-primary: #303133;
}

.el-tag {
  .el-tag__content {
    font-size: 14px;
  }
}

.el-divider--horizontal {
  --el-border-color: var(--app-border-color);
}

.el-radio-group {
  --el-fill-color-blank: transparent;
  --el-border: 1px solid var(--app-border-color);
}

.el-steps{
  --el-bg-color: #022e48;
  --el-text-color-placeholder: var(--app-border-color);
  .el-step__icon.is-text{
    border-width: 1px;
  }
  .el-step__title.is-process,.el-step__icon-inner{
    font-weight: unset;
  }
}