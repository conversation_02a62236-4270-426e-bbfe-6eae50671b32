:root {
  --login-bg-color: #293146;

  --left-menu-max-width: 200px;

  --left-menu-min-width: 64px;

  --left-menu-bg-color: #001529;

  --left-menu-bg-light-color: #0f2438;

  --left-menu-bg-active-color: var(--el-color-primary);

  --left-menu-text-color: #fff;

  --left-menu-text-active-color: #a2ddff;

  --left-menu-collapse-bg-active-color: var(--el-color-primary);
  /* left menu end */

  /* logo start */
  --logo-height: 44px;

  --logo-title-text-color: #fff;
  /* logo end */

  /* header start */
  --top-header-bg-color: '#003f63';

  --top-header-text-color: 'inherit';

  --top-header-hover-color: #f6f6f6;

  --top-tool-height: var(--logo-height);

  --top-tool-p-x: 0;

  --tags-view-height: 35px;
  /* header start */

  /* tab menu start */
  --tab-menu-max-width: 80px;

  --tab-menu-min-width: 30px;

  --tab-menu-collapse-height: 36px;
  /* tab menu end */

  --app-content-padding: 0;

  --app-content-bg-color: #002f49;

  --app-footer-height: 50px;

  --transition-time-02: 0.2s;

  --app-border-color: #2ca1e3;

  --el-fill-color-light: transparent;
}
@font-face {
  font-family: Regular;
  src: url('@/assets/font/regular.OTF');
}
.dark {
  --app-content-bg-color: var(--el-bg-color);
}

html,
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: Regular;
  color: #fff;
}

*,
:after,
:before {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-size: 15px;
}
.overflow_hidden {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
