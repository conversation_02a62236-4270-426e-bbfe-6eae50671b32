<script setup lang="ts">
import { propTypes } from '@/utils/propTypes'
import * as xwRoleApi from '@/api/zygl/xwjs/index'
import * as SeatApi from '@/api/zygl/xlxw/index'
import { xwRoleType } from '@/views/Zygl/xwjs/index'
import { seatType, NetworkType } from '@/views/Zygl/xlxw/index'
import type { EChartsOption } from 'echarts'
import Echart from '@/components/Echart/src/Echart.vue'

const message = useMessage()

type CurrentSeatsType = {
  roleId: number
  seatId: number
  seatList: seatType[]
  network: NetworkType[]
  sys: []
  memoryOptions: EChartsOption
  cpuOptions: EChartsOption
  disksOptions: EChartsOption
}

const props = defineProps({
  label: propTypes.string.def('')
})

const xwRoleList = ref<xwRoleType[]>([])
const seatList = ref<seatType[]>([])

const disabledUp = computed(() => {
  const { seatId, seatList } = currentSeats.value
  if (!seatId) return true
  const seat = seatList.find((i) => i.id === seatId)
  return seat?.stat.up
})

const disabledDown = computed(() => {
  const { seatId, seatList } = currentSeats.value
  if (!seatId) return true
  const seat = seatList.find((i) => i.id === seatId)
  return !seat?.stat.up
})
const currentSeats = ref<CurrentSeatsType>({
  roleId: 0,
  seatId: 0,
  seatList: [],
  network: [],
  sys: [],
  memoryOptions: {
    tooltip: { trigger: 'item', formatter: '{d}%' },
    legend: {
      orient: 'vertical',
      right: 10,
      itemGap: 10,
      top: 10,
      textStyle: {
        color: '#fff',
        fontSize: 12
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['0%', '75%'],
        data: [
          {
            value: 0,
            name: '已使用'
          },
          {
            value: 100,
            name: '未使用'
          }
        ]
      }
    ]
  },
  cpuOptions: {
    tooltip: { trigger: 'item', formatter: '{d}%' },
    legend: {
      orient: 'vertical',
      right: 10,
      itemGap: 10,
      top: 10,
      textStyle: {
        color: '#fff',
        fontSize: 12
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['0%', '75%'],
        data: [
          {
            value: 0,
            name: '已使用'
          },
          {
            value: 100,
            name: '未使用'
          }
        ]
      }
    ]
  },
  disksOptions: {
    tooltip: { formatter: '{c} GB' },
    legend: {
      textStyle: {
        color: '#fff'
      }
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        color: '#fff'
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#014a6d'
        }
      }
    },
    grid: {
      top: '8%',
      left: '5%',
      right: '5%',
      bottom: '10%',
      containLabel: true
    },
    yAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        color: '#fff'
      }
    },
    series: [
      {
        type: 'bar',
        name: '已使用',
        stack: 'total',
        label: { show: true },
        data: []
      },
      {
        type: 'bar',
        stack: 'total',
        name: '未使用',
        label: { show: true },
        data: []
      }
    ]
  }
})
/** 获取席位角色 */
const getXwRoleList = async () => {
  xwRoleList.value = (await xwRoleApi.getListApi(undefined)).list || []
  return
}

/** 获取席位设备 */
const getSeatList = async () => {
  seatList.value = (await SeatApi.getListStatApi()) || []
  if (xwRoleList.value.length) {
    const role = currentSeats.value.roleId
      ? xwRoleList.value.find((i) => i.id === currentSeats.value.roleId) || xwRoleList.value[0]
      : xwRoleList.value[0]
    handleChangeRole(role)
  }
  return
}

/** 选择席位角色 */
const handleChangeRole = (item: xwRoleType) => {
  currentSeats.value.roleId = item.id
  currentSeats.value.seatList = seatList.value.filter(
    (i) => i.seatRoleId === currentSeats.value.roleId
  )
  if (!currentSeats.value.seatList.length) return clearSeatStat()
  // 原本有选原本 / 有开机选开机 / 没开机选第一个
  let seat = currentSeats.value.seatList[0]
  const up = currentSeats.value.seatList.find((i) => i.stat.up)
  if (up) seat = up
  if (currentSeats.value.seatId) {
    seat =
      currentSeats.value.seatList.find((i) => i.id === currentSeats.value.seatId) ||
      up ||
      currentSeats.value.seatList[0]
  }
  handleChangeSeat(seat)
}

/** 清空设备状态 */
const clearSeatStat = () => {
  const { memoryOptions, cpuOptions, disksOptions } = currentSeats.value
  currentSeats.value.seatId = 0
  currentSeats.value.network = []
  currentSeats.value.sys = []
  if (memoryOptions.series) {
    memoryOptions.series[0].data = [
      {
        value: 0,
        name: '已使用'
      },
      {
        value: 100,
        name: '未使用'
      }
    ]
  }
  if (cpuOptions.series) {
    cpuOptions.series[0].data = [
      {
        value: 0,
        name: '已使用'
      },
      {
        value: 100,
        name: '未使用'
      }
    ]
  }
  if (disksOptions.yAxis) {
    disksOptions.yAxis.data = []
  }
  if (disksOptions.series) {
    disksOptions.series.forEach((i) => (i.data = []))
  }
}

/** 选择设备 */
const handleChangeSeat = (item: seatType) => {
  clearSeatStat()
  const { memoryOptions, cpuOptions, disksOptions } = currentSeats.value
  currentSeats.value.seatId = item.id
  if (!item.stat.up) return
  currentSeats.value.network = item.stat.networkInterfaces || []
  if (memoryOptions.series) {
    memoryOptions.series[0].data = [
      {
        value: item.stat.memory?.usedPercent || 0,
        name: '已使用'
      },
      {
        value: 100 - (item.stat.memory?.usedPercent || 0),
        name: '未使用'
      }
    ]
  }
  if (cpuOptions.series) {
    cpuOptions.series[0].data = [
      {
        value: item.stat.cpu?.total || 0,
        name: '已使用'
      },
      {
        value: 100 - (item.stat.cpu?.total || 0),
        name: '未使用'
      }
    ]
  }
  if (disksOptions.yAxis) {
    disksOptions.yAxis.data = item.stat.disks.map((i) => `${i.name.split(':')[0]}盘`)
  }
  if (disksOptions.series) {
    disksOptions.series[0].data = item.stat.disks.map(
      (i) => `${(i.used / (1024 * 1024 * 1024)).toFixed(0)}`
    )
    disksOptions.series[1].data = item.stat.disks.map(
      (i) => `${(i.free / (1024 * 1024 * 1024)).toFixed(0)}`
    )
  }
}

/** 开机 */
const handleUp = async () => {
  const { seatId } = currentSeats.value
  if (!seatId) return message.warning('请选择席位')
  try {
    await message.confirm('确定要开机吗?')
    await SeatApi.upApi({ id: seatId })
    message.success('发送成功, 请等待设备启动')
  } catch {}
}

/** 关机 */
const handleDown = async () => {
  const { seatId } = currentSeats.value
  if (!seatId) return message.warning('请选择席位')
  try {
    await message.confirm('确定要关机吗?')
    await SeatApi.downApi({ id: seatId })
    message.success('发送成功, 请等待设备关闭')
  } catch {}
}

/** 全部开机 */
const handleAllUp = async () => {
  try {
    await message.confirm('确定要全部开机吗?')
    await SeatApi.allUpApi()
    message.success('发送成功, 请等待设备开启')
  } catch {}
}
/** 全部关机 */
const handleAllDown = async () => {
  try {
    await message.confirm('确定要全部关机吗?')
    await SeatApi.allDownApi()
    message.success('发送成功, 请等待设备关闭')
  } catch {}
}
let st: null | NodeJS.Timeout = null
onMounted(async () => {
  await getXwRoleList()
  await getSeatList()
  st = setInterval(async () => {
    await getXwRoleList()
    await getSeatList()
  }, 3000)
})
onUnmounted(() => {
  st && clearInterval(st)
})
</script>
<template>
  <div class="seatBox h-full w-full">
    <el-row class="title pl-10px pr-10px items-center flex-justify-between">
      <p class="text-16px">{{ props.label }}</p>
      <div>
        <el-button :disabled="disabledUp" @click="handleUp">开机</el-button>
        <el-button :disabled="disabledDown" @click="handleDown">关机</el-button>
        <el-button @click="handleAllUp">全部开机</el-button>
        <el-button @click="handleAllDown">全部关机</el-button>
      </div>
    </el-row>
    <el-row class="h-[calc(100%-40px)]">
      <div class="left w-632px pt-10px pl-10px pr-10px">
        <div class="roleBox flex overflow-x-auto whitespace-nowrap pb-5px">
          <div
            v-for="item in xwRoleList"
            :key="item.id"
            class="role-item cursor-pointer text-14px mr-5px"
            :class="[currentSeats.roleId === item.id ? 'active' : '']"
            @click="handleChangeRole(item)"
          >
            {{ item.name }}
          </div>
        </div>
        <el-row class="equipmentBox max-h-[calc(100%-44px)] overflow-y-auto pt-5px">
          <div
            v-for="(item, index) in currentSeats.seatList"
            :key="item.id"
            class="equipment-item mb-14px cursor-pointer position-relative"
            :class="[index % 2 ? '' : 'mr-36px']"
            @click="handleChangeSeat(item)"
            :style="{ opacity: currentSeats.seatId !== item.id ? '.6' : 'unset' }"
          >
            <p class="label position-absolute">{{ item.name }}</p>
            <img
              :src="`/images/seat_${item.stat.up ? 'on' : 'off'}_bg.png`"
              alt=""
              class="position-absolute status"
            />
          </div>
        </el-row>
      </div>
      <div class="center">
        <div class="networkBox h-50%">
          <div class="title pl-15px">网络状态</div>
          <el-table :data="currentSeats.network" stripe height="calc(100% - 40px)">
            <el-table-column label="网卡名称" prop="name" align="center" />
            <el-table-column label="网络进站" prop="uplinkSpeed" align="center" />
            <el-table-column label="网络出站" prop="downlinkSpeed" align="center" />
          </el-table>
        </div>
        <div class="sysBox h-50%">
          <div class="title pl-15px">软件状态</div>
          <el-table :data="currentSeats.sys" stripe height="calc(100% - 40px)">
            <el-table-column label="进程名称" prop="name" align="center" />
            <el-table-column label="运行状态" prop="c" align="center" />
            <el-table-column label="CPU占有率" prop="j" align="center" />
            <el-table-column label="内存占用" prop="j" align="center" />
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <el-button link type="primary">
                  {{ scope.row.j }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="right">
        <div class="memoryBox h-33.33%"
          ><div class="title pl-15px">内存使用情况</div>
          <Echart
            v-if="currentSeats.seatId && disabledUp"
            :options="currentSeats.memoryOptions"
            width="100%"
            height="calc(100% - 40px)"
          />

          <el-row v-else class="h-[calc(100%-40px)] flex-justify-center flex-col items-center">
            <Empty width="120" font-size="16px" />
          </el-row>
        </div>
        <div class="cpuBox h-33.33%"
          ><div class="title pl-15px">CPU使用情况</div>
          <Echart
            v-if="currentSeats.seatId && disabledUp"
            :options="currentSeats.cpuOptions"
            width="100%"
            height="calc(100% - 40px)"
          />
          <el-row v-else class="h-[calc(100%-40px)] flex-justify-center flex-col items-center">
            <Empty width="120" font-size="16px" />
          </el-row>
        </div>
        <div class="hardBox h-33%"
          ><div class="title pl-15px">硬盘使用情况</div>
          <Echart
            class="pt-5px"
            v-if="currentSeats.seatId && disabledUp"
            :options="currentSeats.disksOptions"
            width="100%"
            height="calc(100% - 40px)"
          />
          <el-row v-else class="h-[calc(100%-40px)] flex-justify-center flex-col items-center">
            <Empty width="120" font-size="16px" />
          </el-row>
        </div>
      </div>
    </el-row>
  </div>
</template>
<style lang="less" scoped>
.seatBox {
  .title {
    height: 40px;
    background-image: linear-gradient(90deg, #0f5077 0%, #002941 100%);
    border: 1px solid var(--app-border-color);
  }

  .left {
    border-left: 1px solid var(--app-border-color);

    .roleBox {
      .role-item {
        padding: 3px 15px;
        border: 1px solid var(--app-border-color);
        border-radius: 6px;
      }

      .active {
        background: #086695;
      }
    }

    .equipmentBox {
      .equipment-item {
        flex: 0 0 calc(50% - 18px);
        height: 87px;
        background: url('/images/seat_bg.png') no-repeat 0 0 / 100% 100%;

        .label {
          top: 50%;
          right: 17px;
          width: 192px;
          height: 34px;
          line-height: 34px;
          text-align: center;
          background: url('/images/seat_label_bg.png') no-repeat 0 0 / 100% 100%;
          transform: translateY(-50%);
        }

        .status {
          top: 5px;
          right: 5px;
        }
      }
    }
  }

  .center {
    border-right: 1px solid var(--app-border-color);
    border-left: 1px solid var(--app-border-color);
    flex: 0 0 calc((100% - 632px) * 0.6);

    .networkBox,
    .sysBox {
      .title {
        line-height: 40px;
        border: unset;
      }
    }
  }

  .right {
    flex: 1;
    border-right: 1px solid var(--app-border-color);

    .hardBox,
    .cpuBox,
    .memoryBox {
      .title {
        line-height: 40px;
        border: unset;
      }
    }
  }
}
</style>
