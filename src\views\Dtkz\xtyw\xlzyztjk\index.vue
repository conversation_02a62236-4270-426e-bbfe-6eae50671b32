<script setup lang="ts">
import Seats from './Seats.vue'
const activeTab = ref(1)
const activeLabel = computed(() => {
  return tabList.find((i) => i.key === activeTab.value)!.value
})
const tabList = [
  {
    key: 1,
    value: '工作席位'
  },
  {
    key: 2,
    value: '服务器'
  },
  {
    key: 3,
    value: '数据库'
  }
]
</script>
<template>
  <div class="h-full p-10px pb-0px">
    <el-row class="tabs">
      <div
        class="mr-15px text-16px tab-item cursor-pointer"
        v-for="item in tabList"
        :key="item.key"
        :class="[activeTab === item.key ? 'active' : '']"
        @click="activeTab = item.key"
        >{{ item.value }}</div
      >
    </el-row>
    <el-row class="h-[calc(100%-31px)]">
      <Seats v-if="activeTab === 1" class="" :label="activeLabel" />
    </el-row>
  </div>
</template>
<style lang="less" scoped>
.tabs {
  .tab-item {
    width: 91px;
    height: 31px;
    line-height: 28px;
    color: #c0c0c0;
    text-align: center;
    background-color: #04486c;
    border: 1px solid var(--app-border-color);
    border-bottom: none;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
  }

  .tab-item:hover {
    border-color: var(--el-color-primary-light-3);
  }

  .active {
    color: #fff;
    background-color: #086695;
    border-color: var(--el-color-primary-light-3);
  }
}
</style>
