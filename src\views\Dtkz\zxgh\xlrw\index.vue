<template>

  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form ref="queryFormRef" :inline="true" :model="queryParams" class="-mb-15px" label-width="90px" @submit.prevent>
      <el-form-item label="任务名称" prop="name">
        <el-input v-model="queryParams.name" class="!w-240px" clearable placeholder="请输入任务名称"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button @click="openForm(defaultForm)">
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table stripe v-loading="loading" :data="list">
      <el-table-column align="center" label="任务名称" prop="name" :show-overflow-tooltip="true" />
      <!-- <el-table-column align="center" label="训练类型" prop="type" :show-overflow-tooltip="true" /> -->
      <el-table-column align="center" label="训练场地" prop="ground" :show-overflow-tooltip="true" />
      <el-table-column align="center" label="参训人员" prop="units" :show-overflow-tooltip="true" />
      <el-table-column align="center" label="训练规模" prop="scale" :show-overflow-tooltip="true" />
      <el-table-column align="center" label="训练描述" prop="description" :show-overflow-tooltip="true" />
      <el-table-column align="center" label="训练席位" prop="seatIds" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-tag v-for="id in scope.row.seatIds" :key="id" class="mr-5px">
            {{ seatIdToNameMap[id] || '-' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="训练想定" prop="scenarioId" :show-overflow-tooltip="true" />
      <el-table-column align="center" label="训练计划" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ planIdToNameMap[scope.row.planId] || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <el-button link type="primary" @click="openForm(scope.row)">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNo" :total="total"
      @pagination="getList" />
  </ContentWrap>
  <TaskForm ref="formRef" @success="getList" :seatRoleList="seatRoleList" :trainingPlanList="trainingPlanList" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as TaskApi from '@/api/dtkz/zxgh/xlrw';
import TaskForm from './TaskForm.vue';
import { TaskType } from './index';
import xwRoleType from '@/views/Zygl/xwjs/index'
import PlanType from '@/views/Dtkz/zxgh/xljh/index'
import * as XwRoleApi from '@/api/zygl/xwjs/index';
import * as PlanApi from '@/api/dtkz/zxgh/xljh';

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

type TrainingPlanType = {
  id: number;
  name: string;
  children: PlanType[]
}
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref<TaskType[]>([]) // 列表的数据
const seatRoleList = ref<xwRoleType[]>([]); // 席位分组列表
const trainingPlanList = ref<TrainingPlanType[]>([]); // 训练计划列表
const scenarioList = ref([]); // 想定列表
const defaultForm = {
  id: 0,
  name: '',
  type: 'afsim',
  ground: '',
  units: '',
  scale: '',
  description: '',
  seatIds: [],
  scenarioId: undefined,
  planId: undefined,
};

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: '',
})
const queryFormRef = ref() // 搜索的表单

/** 查询角色列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TaskApi.getTaskList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (row: TaskType) => {
  formRef.value.open(row)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TaskApi.deleteTask(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

const planIdToNameMap = computed(() => {
  const map: Record<number, string> = {}
  trainingPlanList.value.forEach(group => {
    group.children.forEach(plan => {
      map[plan.id] = plan.name
    })
  })
  return map
})

const seatIdToNameMap = computed(() => {
  const map: Record<number, string> = {}
  seatRoleList.value.forEach(seat => {
    map[seat.id] = seat.name
  })
  return map
})

/** 初始化 **/
onMounted(async () => {
  getList()
  // 获取训练席位
  seatRoleList.value = (await XwRoleApi.getListApi()).list || [];
  // 获取训练计划
  const trainingPlan = (await PlanApi.getPlanList()).list || [];
  const planType = getIntDictOptions(DICT_TYPE.TRAINING_PLAT_TYPE)
  trainingPlanList.value = planType.map(i => {
    const children = trainingPlan.filter(ci => parseInt(ci.type) === i.value);
    return {
      id: i.value,
      name: i.label,
      disabled: children.length ? '' : 'disabled',
      children
    }
  })
  // 获取想定
})
</script>
