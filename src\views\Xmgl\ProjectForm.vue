<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="30%">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="90px"
    >
      <el-form-item label="部门">
        <el-input v-model="formData.label" placeholder="请输入部门名称" />
      </el-form-item>
      <el-form-item label="项目名">
        <el-input v-model="formData.value" placeholder="请输入项目名" />
      </el-form-item>
      <el-form-item label="项目概述">
        <el-input v-model="formData.desc" placeholder="请输入项目概述" />
      </el-form-item>
      <el-form-item label="类型">
        <el-select v-model="formData.colorType" clearable placeholder="请选择项目类型">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SYS_PROJECT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="部署地址">
        <el-input v-model="formData.cssClass" placeholder="请输入部署地址" />
      </el-form-item>
      <el-form-item label="项目图片">
        <ProjectImageUpload v-model="formData.imgIds" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import * as DictDataApi from '@/api/system/dict/dict.data'
import ProjectImageUpload from './components/ProjectImageUpload.vue'
defineOptions({ name: 'SystemRoleForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  sort: 0,
  label: '',
  value: '',
  dictType: 'sys_project',
  status: 0,
  colorType: '',
  cssClass: '',
  remark: '',
  createTime: new Date(),
  desc: '',
  imgIds: []
})
const formRules = reactive({
  name: [{ required: true, message: '角色名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '角色标识不能为空', trigger: 'change' }],
  sort: [{ required: true, message: '显示顺序不能为空', trigger: 'change' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  remark: [{ required: false, message: '备注不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await DictDataApi.getDictData(id)
      formData.value = {
        ...data,
        desc: '',
        imgIds: []
      }

      // 解析remark字段中的图片IDs
      if (data.remark) {
        try {
          const remarkData = JSON.parse(data.remark)
          if (remarkData.desc) {
            formData.value.desc = remarkData.desc
          }
          if (remarkData.imgIds && Array.isArray(remarkData.imgIds)) {
            formData.value.imgIds = remarkData.imgIds
          }
          // 兼容旧的imgUrls格式
          if (remarkData.imgUrls && Array.isArray(remarkData.imgUrls)) {
            formData.value.imgIds = remarkData.imgUrls
          }
        } catch (e) {
          // 如果解析失败，说明remark是普通文本
          formData.value.desc = data.remark
        }
      }
    } finally {
      formLoading.value = false
    }
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    sort: 0,
    label: '',
    value: '',
    dictType: 'sys_project',
    status: 0,
    colorType: '',
    cssClass: '',
    remark: '',
    createTime: new Date(),
    desc: '',
    imgIds: []
  }
  formRef.value?.resetFields()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    // 构造符合 DictDataVO 格式的数据
    const data: DictDataApi.DictDataVO = {
      id: formData.value.id,
      sort: formData.value.sort,
      label: formData.value.label,
      value: formData.value.value,
      dictType: formData.value.dictType,
      status: formData.value.status,
      colorType: formData.value.colorType,
      cssClass: formData.value.cssClass,
      remark: formData.value.remark || formData.value.desc,
      createTime: formData.value.createTime
    }

    // 将图片IDs存储到remark字段中（以JSON格式）
    const remarkData = {
      desc: formData.value.desc,
      imgIds: formData.value.imgIds
    }
    data.remark = JSON.stringify(remarkData)
    if (formType.value === 'create') {
      await DictDataApi.createDictData(data)
      message.success(t('common.createSuccess'))
    } else {
      await DictDataApi.updateDictData(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>
