<template>
  <div class="project-image-preview">
    <div v-if="imageUrls.length === 0" class="no-image">
      <Icon icon="ep:picture" />
      <span>暂无图片</span>
    </div>
    <div v-else-if="imageUrls.length === 1" class="single-image">
      <el-image
        :src="imageUrls[0]"
        :preview-src-list="imageUrls"
        :initial-index="0"
        fit="cover"
        class="preview-image"
        @click="handlePreview(0)"
      />
    </div>
    <div v-else class="multiple-images">
      <el-image
        :src="imageUrls[0]"
        :preview-src-list="imageUrls"
        :initial-index="0"
        fit="cover"
        class="main-image"
        @click="handlePreview(0)"
      />
      <div class="image-count" @click="handlePreview(0)">
        <Icon icon="ep:picture" />
        <span>{{ imageUrls.length }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { createImageViewer } from '@/components/ImageViewer'
import { propTypes } from '@/utils/propTypes'
import { previewFile } from '@/api/infra/file'

defineOptions({ name: 'ProjectImagePreview' })

// 定义组件属性
const props = defineProps({
  remark: propTypes.string.def('') // remark字段，包含图片信息
})

// 根据文件ID生成预览URL
const getPreviewUrl = (fileId: number | string) => {
  return previewFile({ fileId })
}

// 解析图片IDs并生成URLs
const imageUrls = computed(() => {
  if (!props.remark) return []

  try {
    const remarkData = JSON.parse(props.remark)
    if (remarkData.imgIds && Array.isArray(remarkData.imgIds)) {
      return remarkData.imgIds
        .filter(id => id !== null && id !== undefined)
        .map(id => getPreviewUrl(id))
    }
    // 兼容旧的imgUrls格式
    if (remarkData.imgUrls && Array.isArray(remarkData.imgUrls)) {
      return remarkData.imgUrls.filter(url => url && url.trim() !== '')
    }
  } catch (e) {
    // 如果解析失败，说明remark是普通文本，没有图片
  }

  return []
})

// 预览图片
const handlePreview = (index: number = 0) => {
  if (imageUrls.value.length > 0) {
    createImageViewer({
      zIndex: 9999999,
      urlList: imageUrls.value,
      initialIndex: index
    })
  }
}
</script>

<style lang="scss" scoped>
.project-image-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 60px;
  
  .no-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--el-text-color-placeholder);
    font-size: 12px;
    
    .el-icon {
      font-size: 20px;
      margin-bottom: 2px;
    }
  }
  
  .single-image {
    width: 60px;
    height: 60px;
    
    .preview-image {
      width: 100%;
      height: 100%;
      border-radius: 4px;
      cursor: pointer;
      transition: transform 0.2s;
      
      &:hover {
        transform: scale(1.05);
      }
    }
  }
  
  .multiple-images {
    position: relative;
    width: 60px;
    height: 60px;
    
    .main-image {
      width: 100%;
      height: 100%;
      border-radius: 4px;
      cursor: pointer;
      transition: transform 0.2s;
      
      &:hover {
        transform: scale(1.05);
      }
    }
    
    .image-count {
      position: absolute;
      top: 2px;
      right: 2px;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 20px;
      height: 16px;
      padding: 0 4px;
      background: rgba(0, 0, 0, 0.6);
      color: #fff;
      font-size: 10px;
      border-radius: 8px;
      cursor: pointer;
      
      .el-icon {
        font-size: 10px;
        margin-right: 2px;
      }
    }
  }
}

:deep(.el-image__error) {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-placeholder);
  font-size: 12px;
}
</style>
