<template>
  <div class="project-image-upload">
    <el-upload
      v-model:file-list="fileList"
      :accept="fileType.join(',')"
      :action="uploadUrl"
      :before-upload="beforeUpload"
      :class="['upload', drag ? 'no-border' : '']"
      :disabled="disabled"
      :drag="drag"
      :http-request="httpRequest"
      :limit="limit"
      :multiple="true"
      :on-error="uploadError"
      :on-exceed="handleExceed"
      :on-success="uploadSuccess"
      list-type="picture-card"
    >
      <div class="upload-empty">
        <slot name="empty">
          <Icon icon="ep:plus" />
          <span>上传项目图片</span>
        </slot>
      </div>
      <template #file="{ file }">
        <img :src="file.url" class="upload-image" />
        <div class="upload-handle" @click.stop>
          <div class="handle-icon" @click="imagePreview(file.url!)">
            <Icon icon="ep:zoom-in" />
            <span>查看</span>
          </div>
          <div v-if="!disabled" class="handle-icon" @click="handleRemove(file)">
            <Icon icon="ep:delete" />
            <span>删除</span>
          </div>
        </div>
      </template>
    </el-upload>
    <div class="el-upload__tip">
      <slot name="tip">
        <div style="font-size: 12px; color: #999;">
          支持 jpg、jpeg、png、gif 格式，单个文件不超过 {{ fileSize }}MB，最多上传 {{ limit }} 张图片
        </div>
      </slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { UploadFile, UploadProps, UploadUserFile } from 'element-plus'
import { ElNotification } from 'element-plus'
import { createImageViewer } from '@/components/ImageViewer'
import { propTypes } from '@/utils/propTypes'
import { useUpload } from '@/components/UploadFile/src/useUpload'

defineOptions({ name: 'ProjectImageUpload' })

// 定义组件属性
const props = defineProps({
  modelValue: propTypes.array.def([]), // 图片URL数组
  disabled: propTypes.bool.def(false), // 是否禁用
  limit: propTypes.number.def(9), // 最大上传数量
  fileSize: propTypes.number.def(5), // 文件大小限制(MB)
  fileType: propTypes.array.def(['image/jpeg', 'image/jpg', 'image/png', 'image/gif']), // 文件类型
  drag: propTypes.bool.def(false) // 是否支持拖拽
})

// 定义事件
const emit = defineEmits(['update:modelValue'])

const message = useMessage() // 消息弹窗

// 文件列表
const fileList = ref<UploadUserFile[]>([])

// 监听modelValue变化，更新文件列表
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      fileList.value = newVal.map((url: string, index: number) => ({
        name: `image-${index + 1}`,
        url: url,
        uid: Date.now() + index
      }))
    } else {
      fileList.value = []
    }
  },
  { immediate: true }
)

// 监听文件列表变化，更新modelValue
watch(
  fileList,
  (newVal) => {
    const urls = newVal.map(file => file.url).filter(url => url)
    emit('update:modelValue', urls)
  },
  { deep: true }
)

// 查看图片
const imagePreview = (imgUrl: string) => {
  createImageViewer({
    zIndex: 9999999,
    urlList: [imgUrl]
  })
}

// 删除图片
const handleRemove = (file: UploadFile) => {
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
}

// 超出限制处理
const handleExceed = () => {
  message.notifyWarning(`最多只能上传 ${props.limit} 张图片！`)
}

// 获取上传配置
const { uploadUrl, httpRequest } = useUpload()

// 上传前校验
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const imgSize = rawFile.size / 1024 / 1024 < props.fileSize
  const imgType = props.fileType.includes(rawFile.type as any)
  
  if (!imgType) {
    message.notifyWarning('上传图片不符合所需的格式！')
  }
  if (!imgSize) {
    message.notifyWarning(`上传图片大小不能超过 ${props.fileSize}MB！`)
  }
  
  return imgType && imgSize
}

// 图片上传成功
const uploadSuccess: UploadProps['onSuccess'] = (res: any, file: UploadFile): void => {
  message.success('上传成功')
  // 更新文件列表中对应文件的URL
  const targetFile = fileList.value.find(item => item.uid === file.uid)
  if (targetFile && res.data) {
    targetFile.url = res.data
  }
}

// 图片上传错误
const uploadError = () => {
  message.notifyError('图片上传失败，请您重新上传！')
}
</script>

<style lang="scss" scoped>
.project-image-upload {
  .upload {
    :deep(.el-upload) {
      position: relative;
      overflow: hidden;
      cursor: pointer;
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      transition: var(--el-transition-duration-fast);
      
      &:hover {
        border-color: var(--el-color-primary);
      }
    }
    
    :deep(.el-upload-dragger) {
      padding: 40px;
      
      &:hover {
        border-color: var(--el-color-primary);
      }
    }
  }
  
  .upload-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 148px;
    height: 148px;
    color: var(--el-text-color-secondary);
    
    .el-icon {
      font-size: 28px;
      margin-bottom: 8px;
    }
    
    span {
      font-size: 12px;
    }
  }
  
  .upload-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .upload-handle {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.6);
    opacity: 0;
    transition: opacity 0.3s;
    
    &:hover {
      opacity: 1;
    }
    
    .handle-icon {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 8px;
      margin: 0 4px;
      color: #fff;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
      
      .el-icon {
        font-size: 16px;
        margin-bottom: 2px;
      }
      
      span {
        font-size: 12px;
      }
    }
  }
  
  .el-upload__tip {
    margin-top: 8px;
    text-align: left;
  }
}

.no-border {
  :deep(.el-upload) {
    border: none !important;
  }
}
</style>
