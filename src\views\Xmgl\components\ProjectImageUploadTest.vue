<template>
  <div class="test-page">
    <h2>项目图片上传测试</h2>
    
    <el-card class="test-card">
      <template #header>
        <span>项目图片上传组件测试</span>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="项目名称">
          <el-input v-model="testForm.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        
        <el-form-item label="项目图片">
          <ProjectImageUpload v-model="testForm.imageIds" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSubmit">提交测试</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card class="test-card" style="margin-top: 20px;">
      <template #header>
        <span>项目图片预览组件测试</span>
      </template>
      
      <div v-if="testForm.imageIds.length > 0">
        <p>当前图片数量：{{ testForm.imageIds.length }}</p>
        <p>图片IDs：</p>
        <ul>
          <li v-for="(id, index) in testForm.imageIds" :key="index">
            {{ id }}
          </li>
        </ul>
        
        <div style="margin-top: 20px;">
          <span>预览组件效果：</span>
          <ProjectImagePreview :remark="mockRemark" />
        </div>
      </div>
      <div v-else>
        <p>暂无图片数据</p>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import ProjectImageUpload from './ProjectImageUpload.vue'
import ProjectImagePreview from './ProjectImagePreview.vue'

defineOptions({ name: 'ProjectImageUploadTest' })

const message = useMessage()

// 测试表单数据
const testForm = reactive({
  projectName: '',
  imageIds: []
})

// 模拟remark数据用于预览组件测试
const mockRemark = computed(() => {
  if (testForm.imageIds.length === 0) return ''

  return JSON.stringify({
    desc: '这是一个测试项目',
    imgIds: testForm.imageIds
  })
})

// 提交测试
const handleSubmit = () => {
  console.log('提交的数据：', testForm)
  message.success(`提交成功！项目名称：${testForm.projectName}，图片数量：${testForm.imageIds.length}`)
}

// 重置表单
const handleReset = () => {
  testForm.projectName = ''
  testForm.imageIds = []
  message.info('表单已重置')
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20px;
  
  .test-card {
    max-width: 800px;
  }
}
</style>
