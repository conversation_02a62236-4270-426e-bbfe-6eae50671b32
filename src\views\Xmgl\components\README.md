# 项目图片上传组件

基于现有头像上传代码实现的项目多图片上传功能。

## 组件说明

### 1. ProjectImageUpload.vue - 项目图片上传组件

**功能特点：**
- 支持多图片上传（默认最多9张）
- 支持拖拽上传
- 图片预览和删除
- 文件类型和大小校验
- 基于现有的 `UploadImgs.vue` 组件改造

**使用方法：**
```vue
<template>
  <ProjectImageUpload v-model="imageUrls" />
</template>

<script setup>
import ProjectImageUpload from './components/ProjectImageUpload.vue'

const imageUrls = ref([])
</script>
```

**Props：**
- `modelValue`: 图片URL数组
- `disabled`: 是否禁用（默认：false）
- `limit`: 最大上传数量（默认：9）
- `fileSize`: 文件大小限制MB（默认：5）
- `fileType`: 支持的文件类型（默认：['image/jpeg', 'image/jpg', 'image/png', 'image/gif']）
- `drag`: 是否支持拖拽（默认：false）

### 2. ProjectImagePreview.vue - 项目图片预览组件

**功能特点：**
- 在列表中显示项目图片缩略图
- 支持单图和多图显示
- 点击可预览大图
- 多图时显示图片数量

**使用方法：**
```vue
<template>
  <ProjectImagePreview :remark="project.remark" />
</template>

<script setup>
import ProjectImagePreview from './components/ProjectImagePreview.vue'
</script>
```

**Props：**
- `remark`: 项目的remark字段，包含JSON格式的图片信息

### 3. ProjectImageUploadTest.vue - 测试组件

用于测试上传和预览组件的功能。

## 数据存储格式

项目图片信息存储在字典数据的 `remark` 字段中，格式为JSON：

```json
{
  "desc": "项目描述",
  "imgUrls": [
    "http://example.com/image1.jpg",
    "http://example.com/image2.jpg"
  ]
}
```

## 集成说明

### 在 ProjectForm.vue 中的集成

1. 添加了 `imgUrls` 字段到表单数据
2. 使用 `ProjectImageUpload` 组件替换原来的文本输入
3. 在提交时将图片URLs序列化到remark字段
4. 在加载时从remark字段解析图片URLs

### 在项目列表中的集成

1. 在 `index.vue` 中使用 `ProjectImagePreview` 组件
2. 替换原来的缩略图列显示
3. 支持点击预览大图

## 技术实现

### 上传流程
1. 用户选择图片文件
2. 前端校验文件类型和大小
3. 调用现有的文件上传API (`/infra/file/upload`)
4. 获取上传后的文件URL
5. 更新组件的modelValue

### 数据流转
1. 表单提交时：`imgUrls` → JSON字符串 → `remark`字段
2. 数据加载时：`remark`字段 → JSON解析 → `imgUrls`
3. 列表显示时：`remark`字段 → JSON解析 → 预览组件

## 依赖组件

- `@/components/UploadFile/src/useUpload`: 文件上传逻辑
- `@/components/ImageViewer`: 图片预览功能
- `Element Plus Upload`: 基础上传组件
- `Element Plus Image`: 图片显示组件

## 注意事项

1. 确保后端文件上传接口 `/infra/file/upload` 正常工作
2. 图片URL需要可以正常访问
3. 建议设置合理的文件大小限制
4. 考虑图片压缩和CDN优化
