<template>
  <Dialog v-model="dialogVisible" title="发布数据" width="50%" top="7vh">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      label-width="90px"
      label-position="left"
    >
      <el-form-item label="数据格式">
        <el-select v-model="formData.dataSourceType" :disabled="!!activeStep">
          <el-option
            v-for="item in dataSourceMap.get(activeMenu)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          /> </el-select
      ></el-form-item>
      <el-steps :active="activeStep" finish-status="success" align-center>
        <el-step title="选择数据" />
        <el-step title="设置参数" />
        <el-step title="发布完成" />
      </el-steps>
      <el-form-item>
        <div class="w-100% h-535px">
          <template v-if="activeStep === 0">
            <div class="min-h-200px">
              <el-breadcrumb :separator-icon="ArrowRight" class="mt-10px mb-10px">
                <el-breadcrumb-item v-for="(item, index) in selectedPaths" :key="item.viewpath">
                  <span class="cursor-pointer" @click="handleChangePath(index)">{{
                    item.viewpath.slice(1, item.viewpath.length)
                  }}</span>
                </el-breadcrumb-item>
              </el-breadcrumb>
              <el-table
                stripe
                :data="diskPaths"
                class="w-100% files-table"
                @cell-click="handleClickCell"
                height="500"
              >
                <el-table-column width="50">
                  <template #default="scope">
                    <el-radio
                      v-model="formData.path"
                      :disabled="scope.row.fileType === 'File'"
                      @click.stop
                      :value="scope.row.fileRealPath"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="文件路径">
                  <template #default="scope">
                    <el-row class="items-center">
                      <Icon
                        v-if="scope.row.fileType === 'Folder'"
                        icon="ep:folder"
                        :size="16"
                        color="#ffd02f"
                      />
                      <Icon v-else icon="ep:document" :size="16" />
                      <span class="ml-5px">{{
                        scope.row.viewpath.slice(1, scope.row.viewpath.length)
                      }}</span>
                    </el-row>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
          <template v-if="activeStep === 1">
            <div class="min-h-200px pt-15px">
              <el-form-item label="图层名称" class="!mb-10px">
                <el-input v-model="formData.layerName" class="!w-85%" />
              </el-form-item>
              <el-form-item label="数据路径" class="!mb-10px">
                <span>{{ formData.path }}</span>
              </el-form-item>
              <el-form-item label="瓦片格式" class="!mb-10px">
                <el-radio-group
                  v-if="activeMenu === 'IMAGELAYER'"
                  disabled
                  v-model="formData.mimeType"
                >
                  <el-radio value="PNG">PNG</el-radio>
                  <el-radio value="JPEG">JPEG</el-radio>
                  <el-radio value="TIFF">TIFF</el-radio>
                </el-radio-group>
                <el-radio-group
                  v-if="activeMenu === 'DEMLAYER'"
                  disabled
                  v-model="formData.mimeType"
                >
                  <el-radio value="TERRAIN">HeightMap</el-radio>
                  <el-radio value="QuantizedMesh">QuantizedMesh</el-radio>
                  <el-radio value="BIL">BIL</el-radio>
                  <el-radio value="RAW">RAW</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="格网集" class="!mb-10px">
                <el-radio-group disabled v-model="formData.gridSet">
                  <el-radio value="EPSG:4326">EPSG:4326</el-radio>
                  <el-radio value="EPSG:3857">EPSG:3857</el-radio>
                  <el-radio value="EPSG:4490">EPSG:4490</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="层级范围" class="!mb-20px">
                <el-input v-model="formData.minLevel" disabled class="!w-120px" />
                <span class="ml-5px mr-5px">至</span>
                <el-input v-model="formData.maxLevel" disabled class="!w-120px mr-5px" />
                有效的层级范围：0-21
              </el-form-item>
              <div class="layer-result">
                <p>将生成1个图层：</p>
                <p>{{ formData.layerName }} - {{ formData.mimeType }} - {{ formData.gridSet }}</p>
              </div>
            </div>
          </template>
          <template v-if="activeStep === 2">
            <div class="min-h-200px pt-15px">
              <h1 class="flex items-center mb-20px mt-10px">
                <Icon icon="ep:success-filled" color="#67c23a" :size="27" />
                <p class="ml-10px font-size-36px" style="color: #24a5f8">发布成功</p>
              </h1>
              <el-form-item label="图层名称" class="!mb-10px">
                <span>{{ formData.layerName }}</span>
              </el-form-item>
              <el-form-item label="层级范围" class="!mb-10px">
                <span>{{ formData.minLevel }}-{{ formData.maxLevel }}</span>
              </el-form-item>
              <el-form-item label="瓦片格式" class="!mb-10px">
                <span>{{ formData.mimeType === 'TERRAIN' ? 'HeightMap' : formData.mimeType }}</span>
              </el-form-item>
              <el-form-item label="格网集" class="!mb-10px">
                <span>{{ formData.gridSet }}</span>
              </el-form-item>
            </div>
          </template>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <template v-if="activeStep === 0">
        <el-button type="primary" @click="handleNextStep">下一步</el-button>
      </template>
      <template v-if="activeStep === 1">
        <el-button @click="activeStep--">上一步</el-button>
        <el-button type="primary" @click="handleRelease">发布服务</el-button>
      </template>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { cloneDeep } from 'lodash-es'
import { ArrowRight } from '@element-plus/icons-vue'
import dataSourceMap from './dataSourceMap'
import { DiskFileType, LayerType } from './index'
import * as Api from '@/api/zygl/dlxx/index'
const message = useMessage() // 消息弹窗
const activeMenu = inject<Ref<string>>('activeMenu')!
const activeStep = inject<Ref<number>>('activeStep')!
const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const formData = ref({} as LayerType)
const defalutPath = {
  fileRealPath: '',
  fileType: '',
  viewpath: '/目录'
}
const selectedPaths = ref<DiskFileType[]>([defalutPath])
const diskPaths = ref<DiskFileType[]>([])

const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

/** 打开弹窗 */
const open = async (data: LayerType) => {
  activeStep.value = 0
  diskPaths.value = []
  selectedPaths.value = [defalutPath]
  await getDiskPaths()
  formData.value = cloneDeep(data)
  dialogVisible.value = true
}

/** 发布服务 */
const handleRelease = async () => {
  formLoading.value = true
  const params = cloneDeep(formData.value)
  params.dataEntityID = ''
  params.formDM = false
  params.isBase = true
  if (activeMenu.value === 'DEMLAYER') {
    params.isBase = false
    params.isTerrain = true
  }
  try {
    await Api.releaseApi(params)
    // 提示成功，并刷新
    message.success('发布成功')
    emit('success')
  } finally {
    formLoading.value = false
  }
  activeStep.value = 2
}
/** 下一步 */
const handleNextStep = async () => {
  if (!formData.value.path) return message.warning('请选择数据')
  const res = await Api.getDiskPathDataApi({ path: formData.value.path })
  if (activeMenu.value === 'IMAGELAYER' && res.layerType !== 'IMAGELAYER') {
    return message.error('请选择影像数据')
  }
  formData.value.layerName = res.layerName
  formData.value.layerType = res.layerType
  formData.value.mimeType = res.mimeType
  formData.value.gridSet = res.gridSet
  formData.value.minLevel = res.minLevel
  formData.value.maxLevel = res.maxLevel
  formData.value.minX = res.minX
  formData.value.maxX = res.maxX
  formData.value.minY = res.minY
  formData.value.maxY = res.maxY
  activeStep.value++
}
/** 路径跳转 */
const handleChangePath = (index) => {
  selectedPaths.value = selectedPaths.value.slice(0, index + 1)
  getDiskPaths()
}

/** 点击文件 */
const handleClickCell = (row) => {
  if (row.fileType === 'File') return
  formData.value.path = ''
  selectedPaths.value.push(row)
  getDiskPaths()
}

/** 获取磁盘路径列表 */
const getDiskPaths = async () => {
  const res = await Api.getDiskPathsApi({
    filePath: selectedPaths.value[selectedPaths.value.length - 1]?.fileRealPath || ''
  })
  diskPaths.value = res || []
  return
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
<style lang="scss">
.files-table td,
.files-table th.is-leaf {
  cursor: pointer;
}

.layer-result {
  line-height: 22px;
  color: #409eff;
}
</style>
