<template>
  <div id="GEOVISContainer"></div>
</template>

<script setup lang="ts">
import { toLower } from 'lodash-es'
import * as Api from '@/api/zygl/dlxx/index'
import { LayerType } from './index'
const route = useRoute()
const GV = window.GV
const layerData = ref({} as LayerType)
const initMap = () => {
  const { layerName, mimeType, gridSet } = layerData.value
  const viewer = new GV.GeoCanvas('GEOVISContainer', {
    baseLayerPicker: false,
    imageryProvider: new Cesium.WebMapTileServiceImageryProvider({
      url: `${globalConfig.ICResourceUrl}/service/wmts?layer=${layerName}`,
      layer: layerName,
      style: 'default',
      format: `image/${toLower(mimeType)}`,
      tileMatrixSetID: gridSet,
      tilingScheme: new Cesium.GeographicTilingScheme()
    })
    // imageryProvider: new Cesium.WebMapServiceImageryProvider({
    //   url: `http://localhost:8210/service/wms?layer=Global_Image-JPEG-4326&TILED=true`,
    //   layers: 'Global_Image-JPEG-4326',
    //   parameters: {
    //     format: 'image/jpeg'
    //   },
    //   srs: 'EPSG:4326'
    // })
    // imageryProvider: new Cesium.TileMapServiceImageryProvider({
    //   tilingScheme: new Cesium.GeographicTilingScheme(),
    //   url: 'http://localhost:8210/service/tms/1.0.0/Global_Image-JPEG-4326@EPSG:4326@jpeg',
    //   fileExtension: 'jpeg'
    // })
  })
  viewer.scene.morphTo2D(0.1)
  viewer.scene.debugShowFramesPerSecond = true
  return viewer
}

onMounted(async () => {
  const res = await Api.getApi(Number(route.params.id))
  layerData.value = res
  window.viewer = initMap()
})
</script>

<style lang="scss" scoped></style>
