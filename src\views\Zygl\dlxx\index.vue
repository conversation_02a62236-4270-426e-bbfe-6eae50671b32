<template>
  <el-row :gutter="20">
    <el-col :span="4" :xs="24">
      <ContentWrap class="h-1/1">
        <Menu />
      </ContentWrap>
    </el-col>
    <el-col :span="20" :xs="24">
      <ContentWrap class="h-1/1">
        <Table />
      </ContentWrap>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import Menu from './menu.vue'
import Table from './table.vue'
const activeMenu = ref('IMAGELAYER')
const activeStep = ref(0)
provide('activeMenu', activeMenu)
provide('activeStep', activeStep)
</script>

<style lang="scss" scoped></style>
