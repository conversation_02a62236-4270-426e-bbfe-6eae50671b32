<template>
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="90px"
      @submit.prevent
    >
      <el-form-item label="图层名称" prop="layerName">
        <el-input
          v-model="queryParams.layerName"
          placeholder="请输入图层名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"> <Icon icon="ep:search" />搜索 </el-button>
        <el-button @click="resetQuery"> <Icon icon="ep:refresh" />重置 </el-button>
        <el-button @click="openForm(defalutForm)"> <Icon icon="ep:plus" /> 发布服务 </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <ContentWrap>
    <el-table stripe v-loading="loading" :data="list">
      <el-table-column
        label="图层名称"
        prop="layerName"
        min-width="300"
        :show-overflow-tooltip="true"
      />
      <el-table-column align="center" label="瓦片格式">
        <template #default="scope">
          {{ fieldMap(scope.row.mimeType) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="格网集" prop="gridSet" />
      <el-table-column align="center" label="层级范围">
        <template #default="scope">
          <span>{{ scope.row.minLevel }} - {{ scope.row.maxLevel }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="发布状态" prop="tileStatus" />
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <div class="flex justify-center">
            <el-button link type="primary" @click="handlePreview(scope.row)">
              <Icon icon="ep:view" class="mr-3px" /> 预览
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, scope.row)">
              <el-button type="primary" link> <Icon icon="ep:d-arrow-right" /> 更多 </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="showAddRess">
                    <Icon icon="ep:map-location" />服务地址
                  </el-dropdown-item>
                  <el-dropdown-item command="delete">
                    <Icon icon="ep:delete" />删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
  <Form ref="formRef" @success="getList" />
  <ServiceAddress ref="serviceAddressRef" @success="getList" />
</template>

<script setup lang="ts">
import * as Api from '@/api/zygl/dlxx/index'
import Form from './Form.vue'
import ServiceAddress from './serviceAddress.vue'
import router from '@/router'
import { LayerType } from './index'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const activeMenu = inject<Ref<string>>('activeMenu')!
const activeStep = inject<Ref<number>>('activeStep')!
const total = ref(0) // 列表的总页数
const list = ref<LayerType[]>([]) // 列表的数据
const queryFormRef = ref() // 搜索的表单
const serviceAddressRef = ref()
const queryParams = reactive({
  page: 1,
  pageSize: 10,
  layerName: '',
  layerType: activeMenu.value
})
const formRef = ref()
const defalutForm = {
  dataSourceType: 'GEOVIS MBTiles', // 数据格式 -> 后期扩展
  path: '', // 数据路径
  layerName: '', // 图层名称
  mimeType: '', // 瓦片格式
  gridSet: '', // 格网集
  minLevel: 0, // 最小层级
  maxLevel: 0, // 最大层级
  minX: 0,
  maxX: 0,
  minY: 0,
  maxY: 0
}

watch(
  () => activeStep.value,
  (newVal) => {
    if (newVal !== 2) return
    getList()
  }
)
watch(
  () => activeMenu.value,
  () => {
    queryParams.layerType = activeMenu.value
    defalutForm.dataSourceType = 'GEOVIS MBTiles(Terrain)'
    getList()
  }
)
/** 添加/修改操作 */
const openForm = (row: Object) => {
  formRef.value.open(row)
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.page = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 操作分发 */
const handleCommand = (command: string, row) => {
  switch (command) {
    case 'showAddRess':
      handleShowAddress(row)
      break
    case 'delete':
      handleDelete(row.id)
      break
    default:
      break
  }
}

const handlePreview = (row) => {
  if (activeMenu.value === 'IMAGELAYER') {
    const path = `/open/baseImage/${row.id}`
    const routeData = router.resolve(path)
    window.open(routeData.href)
  } else if (activeMenu.value === 'DEMLAYER') {
    const path = `/open/terrain/${row.id}`
    const routeData = router.resolve(path)
    window.open(routeData.href)
  }
}

const handleShowAddress = (row) => {
  serviceAddressRef.value.open(row)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await Api.deleteApi(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

const fieldMap = (field: string) => {
  switch (field) {
    case 'GEOJSON': {
      return 'GeoJSON'
    }
    case 'TERRAIN':
    case 'terrain':
    case 'HEIGHTMAP': {
      return 'HeightMap'
    }
    case 'mesh':
    case 'QUANTIZEDMESH': {
      return 'QuantizedMesh'
    }
    default:
      return field
  }
}

const getList = async () => {
  loading.value = true
  try {
    const data = await Api.getListApi(queryParams)
    list.value = data?.data || []
    total.value = data?.total || 0
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped></style>
