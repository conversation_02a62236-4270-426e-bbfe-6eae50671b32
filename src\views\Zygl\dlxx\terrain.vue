<template>
  <div id="GEOVISContainer"></div>
</template>

<script setup lang="ts">
import { isEmpty, toLower } from 'lodash-es'
import * as Api from '@/api/zygl/dlxx/index'
import { LayerType } from './index'
const layerData = ref({} as LayerType)
const terrainData = ref({} as LayerType)
const route = useRoute()
const GV = window.GV

const initMap = () => {
  const { layerName, mimeType, gridSet } = layerData.value
  const viewer = new GV.GeoCanvas(
    'GEOVISContainer',
    isEmpty(layerData)
      ? {}
      : {
          baseLayerPicker: false,
          imageryProvider: new Cesium.WebMapTileServiceImageryProvider({
            url: `${globalConfig.ICResourceUrl}/service/wmts?layer=${layerName}`,
            layer: layerName,
            style: 'default',
            format: `image/${toLower(mimeType)}`,
            tileMatrixSetID: gridSet,
            tilingScheme: new Cesium.GeographicTilingScheme()
          })
        }
  )
  viewer.terrainProvider = new Cesium.CesiumTerrainProvider({
    url: `${globalConfig.ICResourceUrl}/service/terrain/${terrainData.value.layerName}`
  })
  viewer.scene.debugShowFramesPerSecond = true
  return viewer
}

onMounted(async () => {
  const res = await Api.getApi(Number(route.params.id))
  terrainData.value = res
  // 获取一个级别级别最高的影像数据
  const imgRes = ((await Api.getListApi(undefined)).data || []).filter(
    (i) => i.layerType === 'IMAGELAYER'
  )
  if (imgRes.length) {
    const maxLevel = Math.max(...imgRes.map((i) => i.maxLevel))
    layerData.value = imgRes.find((i) => i.maxLevel === maxLevel) || {}
  }
  window.viewer = initMap()
})
</script>

<style lang="scss" scoped></style>
