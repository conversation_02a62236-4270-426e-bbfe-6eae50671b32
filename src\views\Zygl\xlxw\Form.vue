<template>
  <Dialog v-model="dialogVisible" :title="`${formData.id ? '编辑' : '新增'}席位`" width="25%">
    <el-form ref="formRef" v-loading="formLoading" :model="formData" label-width="110px">
      <el-form-item label="席位名称">
        <el-input v-model="formData.name" class="!w-100%" placeholder="请输入席位名称" clearable />
      </el-form-item>
      <el-form-item label="席位ip">
        <el-input
          v-model="formData.deviceIp"
          class="!w-100%"
          placeholder="请输入席位ip"
          clearable
        />
      </el-form-item>
      <el-form-item label="席位mac地址">
        <el-input
          v-model="formData.deviceMac"
          class="!w-100%"
          placeholder="请输入席位mac地址"
          clearable
        />
      </el-form-item>
      <el-form-item label="席位角色">
        <el-select
          v-model="formData.seatRoleId"
          placeholder="请选择席位角色"
          clearable
          class="!w-100%"
          filterable
        >
          <el-option
            v-for="item in seadRoleList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitFileForm">确 定</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as Api from '@/api/zygl/xlxw/index'
import * as seatRoleApi from '@/api/zygl/xwjs/index'
import { cloneDeep } from 'lodash-es'
import { seatType } from './index'
import xwRoleType from '@/views/Zygl/xwjs/index'
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const formData = ref({} as seatType)
const seadRoleList = ref<xwRoleType[]>([])
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

/** 打开弹窗 */
const open = async (data: seatType) => {
  formData.value = cloneDeep(data)
  dialogVisible.value = true
}

/** 提交表单 */
const submitFileForm = async () => {
  formLoading.value = true
  const params = cloneDeep(formData.value)
  try {
    !params.id ? await Api.createApi(params) : await Api.updateApi(params)
    // 清理
    dialogVisible.value = false
    // 提示成功，并刷新
    message.success(t(!params.id ? 'common.createSuccess' : 'common.updateSuccess'))
    emit('success')
  } finally {
    formLoading.value = false
  }
}

onMounted(async () => {
  seadRoleList.value = (await seatRoleApi.getListApi()).list || []
})

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>
