export interface seatType {
  id: number
  name: string // 席位名称
  deviceIp: string // 席位ip
  deviceMac: string // 席位mac地址
  seatRoleId: number | undefined // 角色
  stat: {
    agenProt: number // 席位端口
    timestamp: number
    up: boolean // 在线状态
    cpu: CpuType // cpu
    disks: DisksType[] // 磁盘
    host: HostType // 主机信息
    memory: MemoryType // 内存
    networkInterfaces: NetworkType[] // 网络
  }
}

export interface CpuType {
  cores: number // 核心数
  percentages: number[] // 所有线程占用率
  threads: number // 线程数量
  total: number // cpu占用率
}

export interface DisksType {
  name: string // 磁盘名称
  free: number // 磁盘剩余容量
  total: number // 磁盘总容量
  used: number // 磁盘已用容量
  usedPercent: number // 磁盘使用率百分比
}

export interface HostType {
  arch: string // 系统架构
  bootTime: number // 系统启动时间
  hostname: string // 主机名
  os: string // 系统名称
  upTime: number // 系统运行时间
}

export interface MemoryType {
  free: number // 空闲内存
  total: number // 总内存
  usedPercent: number // 使用内存占比
}

export interface NetworkType {
  addresses: string[] // IP 地址列表
  bytesRecv: number // 接收的字节数
  bytesSent: number // 发送的字节数
  downlinkSpeed: number // 下行速度
  name: string // 网络接口名称
  up: boolean // 是否启用
  uplinkSpeed: number // 上行速度
}
