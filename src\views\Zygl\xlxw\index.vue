<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="90px"
      @submit.prevent
    >
      <el-form-item label="席位名称" prop="name">
        <el-input
          v-model="queryParams.name"
          class="!w-240px"
          clearable
          placeholder="请输入席位名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="席位角色" prop="seatRoleId">
        <el-select
          v-model="queryParams.seatRoleId"
          placeholder="请选择席位角色"
          filterable
          clearable
          class="!w-240px"
          @change="handleQuery"
          clearble
        >
          <el-option
            v-for="item in seadRoleList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon class="mr-5px" icon="ep:refresh" />
          重置
        </el-button>
        <el-button @click="openForm(defaultForm)">
          <Icon class="mr-5px" icon="ep:plus" />
          新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table stripe v-loading="loading" :data="list">
      <el-table-column align="center" label="序号" prop="id" />
      <el-table-column align="center" label="席位名称" prop="name" />
      <el-table-column align="center" label="席位ip" prop="deviceIp" />
      <el-table-column align="center" label="席位mac地址" prop="deviceMac" />
      <el-table-column align="center" label="席位角色">
        <template #default="scope">
          <el-tag>{{ seadRoleList.find((i) => i.id === scope.row.seatRoleId).name || '' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template #default="scope">
          <el-button link type="primary" @click="openForm(scope.row)"> 编辑 </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
  <TaskForm ref="formRef" @success="getList" />
</template>
<script lang="ts" setup>
import * as Api from '@/api/zygl/xlxw/index'
import * as seatRoleApi from '@/api/zygl/xwjs/index'
import TaskForm from './Form.vue'
import { seatType } from './index'
import { xwRoleType } from '@/views/Zygl/xwjs/index'
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref<seatType[]>([]) // 列表的数据
const seadRoleList = ref<xwRoleType[]>([])
const defaultForm = {
  id: 0,
  name: '',
  deviceIp: '',
  deviceMac: '',
  seatRoleId: undefined
}

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: '',
  seatRoleId: undefined
})
const queryFormRef = ref() // 搜索的表单

/** 查询角色列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await Api.getListApi(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (row: seatType) => {
  formRef.value.open(row)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await Api.deleteApi(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(async () => {
  seadRoleList.value = (await seatRoleApi.getListApi()).list || []
  getList()
})
</script>
